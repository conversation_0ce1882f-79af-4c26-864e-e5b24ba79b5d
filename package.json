{"name": "fibra-epsilon", "displayName": "Fibra epsilon", "version": "0.0.1", "description": "Um assistente do SOGE para problemas de integração", "author": "<EMAIL>", "scripts": {"dev": "plasmo dev", "build": "plasmo build", "package": "plasmo package"}, "dependencies": {"@icons-pack/react-simple-icons": "^13.6.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-use-controllable-state": "^1.2.2", "@shikijs/transformers": "^3.8.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.525.0", "plasmo": "0.90.5", "react": "18.2.0", "react-dom": "18.2.0", "react-markdown": "^10.1.0", "remark-gfm": "^4.0.1", "shiki": "^3.8.1", "tailwind-merge": "^3.3.1", "tw-animate-css": "^1.3.5", "use-stick-to-bottom": "^1.1.1"}, "devDependencies": {"@ianvs/prettier-plugin-sort-imports": "4.1.1", "@types/chrome": "0.0.258", "@types/node": "20.11.5", "@types/react": "18.2.48", "@types/react-dom": "18.2.18", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "prettier": "3.2.4", "tailwindcss": "^3.4.17", "typescript": "5.3.3"}, "manifest": {"host_permissions": ["https://*/*"], "action": {"default_popup": "popup.html"}}}